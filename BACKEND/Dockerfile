# Backend Dockerfile for FastAPI
FROM python:3.11-slim

WORKDIR /app

# Install git
RUN apt-get update && apt-get install -y git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0
COPY requirements_cpu.txt ./
RUN pip install --no-cache-dir -r requirements_cpu.txt

# Copy backend code
COPY . .

EXPOSE 8000

CMD ["uvicorn", "fast_api:app", "--host", "0.0.0.0", "--reload"]
