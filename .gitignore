# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

websocket.py
duyet3
runs
DJANGO
ideal.py
streamlit_live_test_model.py
tempCodeRunnerFile.py
__pycache__
temp
training.ipynb
video_test
main.py
.vscode
.env
flask_api
LLMCHAT_testing